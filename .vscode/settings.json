{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}}