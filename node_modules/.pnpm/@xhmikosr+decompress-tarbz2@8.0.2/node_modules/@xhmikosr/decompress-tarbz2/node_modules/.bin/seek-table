#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/seek-bzip@2.0.0/node_modules/seek-bzip/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/seek-bzip@2.0.0/node_modules/seek-bzip/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/seek-bzip@2.0.0/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/seek-bzip@2.0.0/node_modules/seek-bzip/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/seek-bzip@2.0.0/node_modules/seek-bzip/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/seek-bzip@2.0.0/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../seek-bzip@2.0.0/node_modules/seek-bzip/bin/seek-bzip-table" "$@"
else
  exec node  "$basedir/../../../../../../seek-bzip@2.0.0/node_modules/seek-bzip/bin/seek-bzip-table" "$@"
fi
