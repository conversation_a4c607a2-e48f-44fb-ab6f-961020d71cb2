declare const _default: {
    extends: string[];
    rules: {
        'dot-notation': "off";
        '@typescript-eslint/dot-notation': "error";
        '@typescript-eslint/non-nullable-type-assertion-style': "error";
        '@typescript-eslint/prefer-find': "error";
        '@typescript-eslint/prefer-includes': "error";
        '@typescript-eslint/prefer-nullish-coalescing': "error";
        '@typescript-eslint/prefer-optional-chain': "error";
        '@typescript-eslint/prefer-regexp-exec': "error";
        '@typescript-eslint/prefer-string-starts-ends-with': "error";
    };
};
export = _default;
//# sourceMappingURL=stylistic-type-checked-only.d.ts.map