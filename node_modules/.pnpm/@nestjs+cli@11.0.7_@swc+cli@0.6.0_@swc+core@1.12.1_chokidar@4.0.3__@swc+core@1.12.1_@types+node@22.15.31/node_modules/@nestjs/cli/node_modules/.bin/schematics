#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules/@angular-devkit/schematics-cli/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules/@angular-devkit/schematics-cli/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules/@angular-devkit/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules/@angular-devkit/schematics-cli/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules/@angular-devkit/schematics-cli/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules/@angular-devkit/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules/@angular-devkit/schematics-cli/bin/schematics.js" "$@"
else
  exec node  "$basedir/../../../../../../@angular-devkit+schematics-cli@19.2.8_@types+node@22.15.31_chokidar@4.0.3/node_modules/@angular-devkit/schematics-cli/bin/schematics.js" "$@"
fi
