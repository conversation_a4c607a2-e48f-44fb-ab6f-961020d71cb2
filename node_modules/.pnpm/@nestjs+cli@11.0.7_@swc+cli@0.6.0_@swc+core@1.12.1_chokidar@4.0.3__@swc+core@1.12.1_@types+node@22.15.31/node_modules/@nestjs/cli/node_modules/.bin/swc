#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules/@swc/cli/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules/@swc/cli/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules/@swc/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules/@swc/cli/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules/@swc/cli/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules/@swc/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules/@swc/cli/bin/swc.js" "$@"
else
  exec node  "$basedir/../../../../../../@swc+cli@0.6.0_@swc+core@1.12.1_chokidar@4.0.3/node_modules/@swc/cli/bin/swc.js" "$@"
fi
