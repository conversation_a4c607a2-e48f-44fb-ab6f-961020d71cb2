#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/mkdirp@0.5.6/node_modules/mkdirp/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/mkdirp@0.5.6/node_modules/mkdirp/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/mkdirp@0.5.6/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/mkdirp@0.5.6/node_modules/mkdirp/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/mkdirp@0.5.6/node_modules/mkdirp/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/mkdirp@0.5.6/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../mkdirp/bin/cmd.js" "$@"
else
  exec node  "$basedir/../mkdirp/bin/cmd.js" "$@"
fi
