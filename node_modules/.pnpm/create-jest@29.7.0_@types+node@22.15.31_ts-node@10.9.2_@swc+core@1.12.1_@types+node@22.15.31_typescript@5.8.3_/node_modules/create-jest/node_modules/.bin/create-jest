#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.31_ts-node@10.9.2_@swc+core@1.12.1_@types+node@22.15.31_typescript@5.8.3_/node_modules/create-jest/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.31_ts-node@10.9.2_@swc+core@1.12.1_@types+node@22.15.31_typescript@5.8.3_/node_modules/create-jest/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.31_ts-node@10.9.2_@swc+core@1.12.1_@types+node@22.15.31_typescript@5.8.3_/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.31_ts-node@10.9.2_@swc+core@1.12.1_@types+node@22.15.31_typescript@5.8.3_/node_modules/create-jest/bin/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.31_ts-node@10.9.2_@swc+core@1.12.1_@types+node@22.15.31_typescript@5.8.3_/node_modules/create-jest/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.31_ts-node@10.9.2_@swc+core@1.12.1_@types+node@22.15.31_typescript@5.8.3_/node_modules:/Users/<USER>/Developer/service-haven-customer-verification/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/create-jest.js" "$@"
else
  exec node  "$basedir/../../bin/create-jest.js" "$@"
fi
